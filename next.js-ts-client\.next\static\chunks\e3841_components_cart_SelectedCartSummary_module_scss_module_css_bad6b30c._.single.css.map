{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.module.scss", "turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../../../src/scss/variables' as *;\n@use '../../../../../src/scss/mixins' as *;\n\n.summary_container {\n  background-color: white;\n  border: 1px solid #d1d5db;\n  border-radius: $border-radius-2;\n  padding: $padding-4;\n  margin-top: $padding-3;\n  box-shadow: $box-shadow-1;\n}\n\n.summary_header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $padding-3;\n  padding-bottom: $padding-2;\n  border-bottom: 1px solid #d1d5db;\n\n  h3 {\n    margin: 0;\n    font-size: $font-size-5;\n    font-weight: map-get($font-weight, 'medium');\n    color: $primary-dark-text-color;\n  }\n\n  .item_count {\n    font-size: $font-size-2;\n    color: $primary-lighter-text-color;\n    background-color: #f9fafb;\n    padding: $padding-1 $padding-2;\n    border-radius: $border-radius-1;\n  }\n}\n\n.summary_details {\n  position: relative;\n}\n\n.summary_row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: $padding-2 0;\n  border-bottom: 1px solid rgba(#d1d5db, 0.5);\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  span {\n    font-size: $font-size-3;\n\n    &:first-child {\n      color: $primary-lighter-text-color;\n    }\n  }\n\n  .price {\n    font-weight: map-get($font-weight, 'medium');\n    color: $primary-dark-text-color;\n    font-size: $font-size-3;\n  }\n}\n\n.comparison_section {\n  margin-top: $padding-3;\n\n  .divider {\n    height: 1px;\n    background-color: #d1d5db;\n    margin: $padding-3 0 $padding-2 0;\n  }\n\n  .comparison_header {\n    margin-bottom: $padding-2;\n\n    span {\n      font-size: $font-size-2;\n      font-weight: map-get($font-weight, 'medium');\n      color: $primary-lighter-text-color;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n  }\n\n  .comparison_row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: $padding-1 0;\n    font-size: $font-size-2;\n\n    .savings {\n      color: $primary-green;\n      font-weight: map-get($font-weight, 'medium');\n    }\n  }\n}\n\n.no_selection {\n  text-align: center;\n  padding: $padding-5 $padding-4;\n  color: $primary-lighter-text-color;\n\n  p {\n    margin: 0 0 $padding-1 0;\n    font-size: $font-size-3;\n    font-weight: map-get($font-weight, 'medium');\n  }\n\n  span {\n    font-size: $font-size-2;\n    color: #9ca3af;\n  }\n}\n\n.loading_overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(white, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: $border-radius-2;\n\n  span {\n    font-size: $font-size-2;\n    color: $primary-lighter-text-color;\n    font-style: italic;\n  }\n}\n\n// Mobile responsiveness\n@media (max-width: $mobile) {\n  .summary_container {\n    padding: $padding-3;\n    margin-top: $padding-2;\n  }\n\n  .summary_header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: $padding-1;\n\n    h3 {\n      font-size: $font-size-3;\n    }\n\n    .item_count {\n      font-size: $font-size-1;\n    }\n  }\n\n  .summary_row {\n    padding: $padding-1 0;\n\n    span {\n      font-size: $font-size-2;\n    }\n  }\n\n  .comparison_row {\n    font-size: $font-size-1;\n  }\n}\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAGA;;;;;;;;;AASA;;;;;;;;;AAQE;;;;;;;AAOA;;;;;;;;AASF;;;;AAIA;;;;;;;;AAOE;;;;AAIA;;;;AAGE;;;;AAKF;;;;;;AAOF;;;;AAGE;;;;;;AAMA;;;;AAGE;;;;;;;;AASF;;;;;;;;AAOE;;;;;AAOJ;;;;;;AAKE;;;;;;AAMA;;;;;AAMF;;;;;;;;;;;;;AAYE;;;;;;AAQF;EACE;;;;;EAKA;;;;;;EAKE;;;;EAIA;;;;EAKF;;;;EAGE;;;;EAKF"}}]}