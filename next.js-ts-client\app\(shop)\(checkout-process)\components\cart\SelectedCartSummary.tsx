'use client'

import React, { useEffect, useState } from 'react'
import { useSelectedCartSummary } from '@/src/hooks/cart-selection-hooks'
import cartStore from '@/src/stores/cart-store'
import { CartItemShape } from '@/src/types/store-types'
import styles from './SelectedCartSummary.module.scss'

interface SelectedCartSummaryProps {
  cartItems: CartItemShape[]
  showComparison?: boolean
  selectedIds?: Set<number>
}

const SelectedCartSummary = ({
  cartItems,
  showComparison = false,
  selectedIds,
}: SelectedCartSummaryProps) => {
  const selectedItemIds = cartStore((s) => s.selectedItemIds)
  const { fetchSummary } = useSelectedCartSummary()
  interface SummaryData {
    selected_shipping_cost?: number
    selected_total_weight?: number
  }

  const [summaryData, setSummaryData] = useState<SummaryData | null>(null)
  const [loading, setLoading] = useState(false)

  // Prefer server-provided selection when available, otherwise fall back to persisted array
  const safeSelectedCartItems = React.useMemo(() => {
    if (selectedIds && selectedIds instanceof Set)
      return new Set<number>(selectedIds)
    return new Set<number>(
      Array.isArray(selectedItemIds) ? selectedItemIds : []
    )
  }, [selectedIds, selectedItemIds])

  useEffect(() => {
    let mounted = true

    const loadSummary = async () => {
      if (safeSelectedCartItems.size === 0) {
        if (mounted) setSummaryData(null)
        return
      }

      if (mounted) setLoading(true)
      try {
        const data = await fetchSummary()
        if (mounted) setSummaryData(data)
      } catch (error) {
        console.error('Error fetching selected cart summary:', error)
      } finally {
        if (mounted) setLoading(false)
      }
    }

    loadSummary()

    return () => {
      mounted = false
    }
  }, [fetchSummary, safeSelectedCartItems.size])

  // Calculate frontend totals for selected items
  const selectedItems = cartItems.filter((item) =>
    safeSelectedCartItems.has(item.id)
  )
  const selectedCount = selectedItems.length
  const totalCount = cartItems.length
  const selectedSubtotal = selectedItems.reduce(
    (sum, item) => sum + item.qty_price,
    0
  )
  const totalSubtotal = cartItems.reduce((sum, item) => sum + item.qty_price, 0)

  if (selectedCount === 0) {
    return (
      <div className={styles.summary_container}>
        <div className={styles.no_selection}>
          <p>No items selected</p>
          <span>Select items to see checkout summary</span>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.summary_container}>
      <div className={styles.summary_header}>
        <h3>Selected Items Summary</h3>
        <span className={styles.item_count}>
          {selectedCount} of {totalCount} items selected
        </span>
      </div>

      <div className={styles.summary_details}>
        <div className={styles.summary_row}>
          <span>Selected Subtotal:</span>
          <span className={styles.price}>${selectedSubtotal.toFixed(2)}</span>
        </div>

        {summaryData && (
          <>
            <div className={styles.summary_row}>
              <span>Shipping (estimated):</span>
              <span className={styles.price}>
                {summaryData.selected_shipping_cost
                  ? `$${summaryData.selected_shipping_cost}`
                  : 'TBD'}
              </span>
            </div>

            <div className={styles.summary_row}>
              <span>Total Weight:</span>
              <span>{summaryData.selected_total_weight || 0} kg</span>
            </div>
          </>
        )}

        {showComparison && selectedCount < totalCount && (
          <div className={styles.comparison_section}>
            <div className={styles.divider}></div>
            <div className={styles.comparison_header}>
              <span>Comparison</span>
            </div>

            <div className={styles.comparison_row}>
              <span>All Items Subtotal:</span>
              <span className={styles.price}>${totalSubtotal.toFixed(2)}</span>
            </div>

            <div className={styles.comparison_row}>
              <span>Savings by selecting:</span>
              <span className={styles.savings}>
                ${(totalSubtotal - selectedSubtotal).toFixed(2)}
              </span>
            </div>
          </div>
        )}
      </div>

      {loading && (
        <div className={styles.loading_overlay}>
          <span>Calculating...</span>
        </div>
      )}
    </div>
  )
}

export default SelectedCartSummary
